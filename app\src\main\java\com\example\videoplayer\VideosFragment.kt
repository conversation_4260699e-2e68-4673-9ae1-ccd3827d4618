package com.example.videoplayer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class VideosFragment : Fragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var emptyView: TextView
    private lateinit var adapter: VideoAdapter
    
    private var videos = mutableListOf<Video>()
    private var fragmentType = 0 // 0: All, 1: Favorites, 2: Recently Played
    
    companion object {
        private const val ARG_FRAGMENT_TYPE = "fragment_type"
        
        fun newInstance(type: Int): VideosFragment {
            return VideosFragment().apply {
                arguments = Bundle().apply {
                    putInt(ARG_FRAGMENT_TYPE, type)
                }
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            fragmentType = it.getInt(ARG_FRAGMENT_TYPE, 0)
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_videos, container, false)
        recyclerView = view.findViewById(R.id.recyclerView)
        emptyView = view.findViewById(R.id.emptyView)
        return view
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        recyclerView.layoutManager = LinearLayoutManager(context)
        adapter = VideoAdapter(
            videos,
            { video -> (activity as? MainActivity)?.playVideo(video.uri) },
            { video -> toggleFavorite(video) }
        )
        recyclerView.adapter = adapter
        
        updateVideosList()
    }
    
    fun updateVideosList() {
        val mainActivity = activity as? MainActivity ?: return
        
        videos.clear()
        when (fragmentType) {
            0 -> videos.addAll(mainActivity.getAllVideos())
            1 -> videos.addAll(mainActivity.getFavoriteVideos())
            2 -> videos.addAll(mainActivity.getRecentlyPlayedVideos())
        }
        
        adapter.notifyDataSetChanged()
        
        // Show empty view if no videos
        if (videos.isEmpty()) {
            recyclerView.visibility = View.GONE
            emptyView.visibility = View.VISIBLE
        } else {
            recyclerView.visibility = View.VISIBLE
            emptyView.visibility = View.GONE
        }
    }
    
    private fun toggleFavorite(video: Video) {
        video.isFavorite = !video.isFavorite
        (activity as? MainActivity)?.updateVideoFavoriteStatus(video)
        
        // If we're in favorites tab and removing from favorites, update the list
        if (fragmentType == 1 && !video.isFavorite) {
            updateVideosList()
        }
    }
}