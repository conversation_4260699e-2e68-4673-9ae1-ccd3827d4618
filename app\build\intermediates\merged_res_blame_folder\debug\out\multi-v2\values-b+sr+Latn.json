{"logs": [{"outputFile": "com.example.videoplayer.app-mergeDebugResources-30:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\660657241239aba9a58fa535da30e4bc\\transformed\\exoplayer-ui-2.18.5\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1946,2060,2171,2247,2335,2409,2480,2572,2665,2732,2797,2850,2908,2956,3017,3083,3147,3210,3275,3339,3400,3466,3531,3597,3649,3711,3787,3863", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1941,2055,2166,2242,2330,2404,2475,2567,2660,2727,2792,2845,2903,2951,3012,3078,3142,3205,3270,3334,3395,3461,3526,3592,3644,3706,3782,3858,3914"}, "to": {"startLines": "2,11,16,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,651,4676,4758,4841,4923,5012,5103,5173,5240,5334,5429,5497,5561,5624,5696,5805,5919,6030,6106,6194,6268,6339,6431,6524,6591,7328,7381,7439,7487,7548,7614,7678,7741,7806,7870,7931,7997,8062,8128,8180,8242,8318,8394", "endLines": "10,15,20,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "377,646,907,4753,4836,4918,5007,5098,5168,5235,5329,5424,5492,5556,5619,5691,5800,5914,6025,6101,6189,6263,6334,6426,6519,6586,6651,7376,7434,7482,7543,7609,7673,7736,7801,7865,7926,7992,8057,8123,8175,8237,8313,8389,8445"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\42c9cce8c7b5fed18c278c11b515454e\\transformed\\core-1.9.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "167", "startColumns": "4", "startOffsets": "12682", "endColumns": "100", "endOffsets": "12778"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\473d6f74a7df0a9331ff7f3743537b94\\transformed\\material-1.8.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1133,1226,1294,1357,1460,1520,1586,1642,1713,1773,1827,1939,1996,2057,2111,2187,2312,2399,2482,2621,2703,2786,2874,2928,2984,3050,3124,3202,3291,3367,3443,3518,3590,3680,3753,3845,3941,4013,4089,4185,4238,4305,4392,4479,4541,4605,4668,4773,4877,4973,5080,5138,5198", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,87,53,55,65,73,77,88,75,75,74,71,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,57,59,79", "endOffsets": "319,396,473,553,661,755,849,981,1062,1128,1221,1289,1352,1455,1515,1581,1637,1708,1768,1822,1934,1991,2052,2106,2182,2307,2394,2477,2616,2698,2781,2869,2923,2979,3045,3119,3197,3286,3362,3438,3513,3585,3675,3748,3840,3936,4008,4084,4180,4233,4300,4387,4474,4536,4600,4663,4768,4872,4968,5075,5133,5193,5273"}, "to": {"startLines": "21,53,54,55,56,57,58,59,60,61,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,3867,3944,4021,4101,4209,4303,4397,4529,4610,8450,8543,8611,8674,8777,8837,8903,8959,9030,9090,9144,9256,9313,9374,9428,9504,9629,9716,9799,9938,10020,10103,10191,10245,10301,10367,10441,10519,10608,10684,10760,10835,10907,10997,11070,11162,11258,11330,11406,11502,11555,11622,11709,11796,11858,11922,11985,12090,12194,12290,12397,12455,12515", "endLines": "25,53,54,55,56,57,58,59,60,61,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,87,53,55,65,73,77,88,75,75,74,71,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,57,59,79", "endOffsets": "1131,3939,4016,4096,4204,4298,4392,4524,4605,4671,8538,8606,8669,8772,8832,8898,8954,9025,9085,9139,9251,9308,9369,9423,9499,9624,9711,9794,9933,10015,10098,10186,10240,10296,10362,10436,10514,10603,10679,10755,10830,10902,10992,11065,11157,11253,11325,11401,11497,11550,11617,11704,11791,11853,11917,11980,12085,12189,12285,12392,12450,12510,12590"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\4874790671c8ee4a83a51a3d7307ebb2\\transformed\\exoplayer-core-2.18.5\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6656,6731,6792,6857,6929,7008,7081,7169,7253", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "6726,6787,6852,6924,7003,7076,7164,7248,7323"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\b0cee708ffbac1b04351a1bcae21fc1d\\transformed\\appcompat-1.6.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1136,1243,1344,1450,1536,1640,1762,1847,1929,2020,2113,2208,2302,2402,2495,2590,2695,2786,2877,2963,3068,3174,3277,3384,3493,3600,3770,12595", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "1238,1339,1445,1531,1635,1757,1842,1924,2015,2108,2203,2297,2397,2490,2585,2690,2781,2872,2958,3063,3169,3272,3379,3488,3595,3765,3862,12677"}}]}]}