package com.example.videoplayer

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import java.util.concurrent.TimeUnit

class VideoAdapter(
    private val videos: List<Video>,
    private val onVideoClick: (Video) -> Unit
) : RecyclerView.Adapter<VideoAdapter.VideoViewHolder>() {

    class VideoViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val thumbnail: ImageView = view.findViewById(R.id.videoThumbnail)
        val title: TextView = view.findViewById(R.id.videoTitle)
        val info: TextView = view.findViewById(R.id.videoInfo)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VideoViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_video, parent, false)
        return VideoViewHolder(view)
    }

    override fun onBindViewHolder(holder: VideoViewHolder, position: Int) {
        val video = videos[position]
        holder.title.text = video.title
        
        // Format duration to minutes:seconds
        val minutes = TimeUnit.MILLISECONDS.toMinutes(video.duration)
        val seconds = TimeUnit.MILLISECONDS.toSeconds(video.duration) - 
                TimeUnit.MINUTES.toSeconds(minutes)
        
        // Format size to MB
        val sizeInMb = video.size / (1024 * 1024f)
        
        holder.info.text = String.format(
            "%02d:%02d • %.1f MB • %s", 
            minutes, seconds, sizeInMb, video.mimeType
        )
        
        holder.itemView.setOnClickListener {
            onVideoClick(video)
        }
    }

    override fun getItemCount() = videos.size
}