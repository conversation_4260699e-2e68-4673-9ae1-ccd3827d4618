package com.example.videoplayer

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.viewpager2.widget.ViewPager2
import com.google.android.exoplayer2.C
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.StyledPlayerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class MainActivity : AppCompatActivity() {

    private lateinit var playerView: StyledPlayerView
    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout
    private lateinit var selectVideoButton: FloatingActionButton
    private var player: ExoPlayer? = null
    private val allVideos = mutableListOf<Video>()
    private lateinit var preferences: SharedPreferences
    private val gson = Gson()

    companion object {
        private const val READ_EXTERNAL_STORAGE_REQUEST = 1
        private const val PICK_VIDEO_REQUEST = 2
        private const val PREFS_NAME = "VideoPlayerPrefs"
        private const val FAVORITE_VIDEOS_KEY = "favorite_videos"
        private const val RECENTLY_PLAYED_KEY = "recently_played"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        preferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        playerView = findViewById(R.id.player_view)
        viewPager = findViewById(R.id.viewPager)
        tabLayout = findViewById(R.id.tabLayout)
        selectVideoButton = findViewById(R.id.selectVideoButton)

        // Initialize ViewPager and TabLayout
        val pagerAdapter = ViewPagerAdapter(this)
        viewPager.adapter = pagerAdapter

        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> getString(R.string.all_videos)
                1 -> getString(R.string.favorites)
                2 -> getString(R.string.recently_played)
                else -> "Tab ${position + 1}"
            }
        }.attach()

        // Set up button click listener
        selectVideoButton.setOnClickListener {
            if (checkStoragePermission()) {
                openVideoSelector()
            } else {
                requestStoragePermission()
            }
        }

        // Check for permission and load videos if granted
        if (checkStoragePermission()) {
            loadDeviceVideos()
        }
        
        // Load saved favorites
        loadFavorites()
        loadRecentlyPlayed()
    }

    private fun initializePlayer() {
        player = ExoPlayer.Builder(this).build().apply {
            // Set repeat mode
            repeatMode = Player.REPEAT_MODE_ALL
            // Enable audio focus handling
            setAudioAttributes(
                com.google.android.exoplayer2.audio.AudioAttributes.Builder()
                    .setUsage(C.USAGE_MEDIA)
                    .setContentType(C.CONTENT_TYPE_MOVIE)
                    .build(),
                true
            )
        }
        playerView.player = player
    }

    fun playVideo(videoUri: Uri) {
        if (player == null) {
            initializePlayer()
        }
        
        // Find the video in our list and update last played time
        allVideos.find { it.uri == videoUri }?.let { video ->
            video.lastPlayed = System.currentTimeMillis()
            saveRecentlyPlayed()
            
            // Update the recently played tab
            updateFragments()
        }
        
        val mediaItem = MediaItem.fromUri(videoUri)
        player?.setMediaItem(mediaItem)
        player?.prepare()
        player?.play()
    }

    private fun checkStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.READ_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestStoragePermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
            READ_EXTERNAL_STORAGE_REQUEST
        )
    }

    private fun openVideoSelector() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "video/*"
        }
        startActivityForResult(intent, PICK_VIDEO_REQUEST)
    }

    private fun loadDeviceVideos() {
        allVideos.clear()
        
        val projection = arrayOf(
            MediaStore.Video.Media._ID,
            MediaStore.Video.Media.DISPLAY_NAME,
            MediaStore.Video.Media.MIME_TYPE,
            MediaStore.Video.Media.SIZE,
            MediaStore.Video.Media.DURATION
        )
        
        val sortOrder = "${MediaStore.Video.Media.DATE_ADDED} DESC"
        
        contentResolver.query(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            projection,
            null,
            null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DISPLAY_NAME)
            val mimeTypeColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.MIME_TYPE)
            val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.SIZE)
            val durationColumn = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DURATION)

            while (cursor.moveToNext()) {
                val id = cursor.getLong(idColumn)
                val name = cursor.getString(nameColumn)
                val mimeType = cursor.getString(mimeTypeColumn)
                val size = cursor.getLong(sizeColumn)
                val duration = cursor.getLong(durationColumn)
                
                val contentUri = Uri.withAppendedPath(
                    MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                    id.toString()
                )
                
                allVideos.add(
                    Video(
                        id = id,
                        title = name,
                        uri = contentUri,
                        mimeType = mimeType,
                        size = size,
                        duration = duration
                    )
                )
            }
        }
        
        // Apply favorite status from saved preferences
        val favoriteIds = getFavoriteVideoIds()
        allVideos.forEach { video ->
            video.isFavorite = favoriteIds.contains(video.id)
        }
        
        updateFragments()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == READ_EXTERNAL_STORAGE_REQUEST) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                loadDeviceVideos()
            } else {
                Toast.makeText(
                    this,
                    R.string.permission_denied,
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PICK_VIDEO_REQUEST && resultCode == Activity.RESULT_OK) {
            data?.data?.let { uri ->
                // Take persistent URI permission
                contentResolver.takePersistableUriPermission(
                    uri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                playVideo(uri)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        if (player == null) {
            initializePlayer()
        }
    }

    override fun onStop() {
        super.onStop()
        releasePlayer()
    }

    private fun releasePlayer() {
        player?.release()
        player = null
    }
    
    // Methods for fragments to access videos
    fun getAllVideos(): List<Video> = allVideos
    
    fun getFavoriteVideos(): List<Video> = allVideos.filter { it.isFavorite }
    
    fun getRecentlyPlayedVideos(): List<Video> {
        return allVideos.filter { it.lastPlayed > 0 }
            .sortedByDescending { it.lastPlayed }
    }
    
    fun updateVideoFavoriteStatus(video: Video) {
        // Update the video in our list
        allVideos.find { it.id == video.id }?.isFavorite = video.isFavorite
        
        // Save to preferences
        saveFavorites()
    }
    
    private fun updateFragments() {
        for (i in 0 until viewPager.adapter!!.itemCount) {
            val fragment = supportFragmentManager.findFragmentByTag("f${viewPager.id}:${i}")
            (fragment as? VideosFragment)?.updateVideosList()
        }
    }
    
    // Preferences methods
    private fun getFavoriteVideoIds(): Set<Long> {
        val json = preferences.getString(FAVORITE_VIDEOS_KEY, "[]")
        val type = object : TypeToken<List<Long>>() {}.type
        return gson.fromJson<List<Long>>(json, type).toSet()
    }
    
    private fun saveFavorites() {
        val favoriteIds = allVideos.filter { it.isFavorite }.map { it.id }
        val json = gson.toJson(favoriteIds)
        preferences.edit().putString(FAVORITE_VIDEOS_KEY, json).apply()
    }
    
    private fun loadFavorites() {
        val favoriteIds = getFavoriteVideoIds()
        allVideos.forEach { video ->
            video.isFavorite = favoriteIds.contains(video.id)
        }
    }
    
    private fun saveRecentlyPlayed() {
        val recentlyPlayed = allVideos
            .filter { it.lastPlayed > 0 }
            .map { it.id to it.lastPlayed }
        val json = gson.toJson(recentlyPlayed)
        preferences.edit().putString(RECENTLY_PLAYED_KEY, json).apply()
    }
    
    private fun loadRecentlyPlayed() {
        val json = preferences.getString(RECENTLY_PLAYED_KEY, "[]")
        val type = object : TypeToken<List<Pair<Long, Long>>>() {}.type
        val recentlyPlayed: List<Pair<Long, Long>> = gson.fromJson(json, type) ?: listOf()
        
        recentlyPlayed.forEach { (id, timestamp) ->
            allVideos.find { it.id == id }?.lastPlayed = timestamp
        }
    }
}