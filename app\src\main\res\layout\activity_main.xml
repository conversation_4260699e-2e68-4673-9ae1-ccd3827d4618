<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <com.google.android.exoplayer2.ui.PlayerView
        android:id="@+id/player_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/recyclerView"
        app:layout_constraintHeight_percent="0.6" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/player_view"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.4" />

    <Button
        android:id="@+id/selectVideoButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Select Video"
        app:layout_constraintBottom_toBottomOf="@id/player_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/player_view" />

</androidx.constraintlayout.widget.ConstraintLayout>