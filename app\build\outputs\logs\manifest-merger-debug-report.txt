-- Merging decision tree log ---
manifest
ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:2:1-25:12
INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml:2:1-25:12
INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml:2:1-25:12
INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml:2:1-25:12
MERGED from [com.google.android.material:material:1.8.0] C:\gradle-home\caches\8.14.1\transforms\473d6f74a7df0a9331ff7f3743537b94\transformed\material-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\gradle-home\caches\8.14.1\transforms\6558c962b139a4ad878360b49ac0923b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\gradle-home\caches\8.14.1\transforms\bb3ab7ebc5cfac50f3aa0218b7126221\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\gradle-home\caches\8.14.1\transforms\b0cee708ffbac1b04351a1bcae21fc1d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\gradle-home\caches\8.14.1\transforms\a6905b79b4f131661046404be177249c\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\gradle-home\caches\8.14.1\transforms\9f87f88ae7c8ae3fa78b0eece36ed6e8\transformed\glide-4.14.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\gradle-home\caches\8.14.1\transforms\2ef9756dc4712abd1352b8b267772b82\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\gradle-home\caches\8.14.1\transforms\b61cef457b792e3299c0090c433369fc\transformed\activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\gradle-home\caches\8.14.1\transforms\11471678fcc1a3ff3a05cb52ee358cb9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\gradle-home\caches\8.14.1\transforms\f5684cc501bccec97e63bf765b7dbb60\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\gradle-home\caches\8.14.1\transforms\e9e7ec9dd0ecbcd7a4bcbae7a80106bc\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\gradle-home\caches\8.14.1\transforms\a2db37f316450a14ac9b67e261a4ffca\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\gradle-home\caches\8.14.1\transforms\b9ac6a1a1737e81aff50b802b7906914\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.5] C:\gradle-home\caches\8.14.1\transforms\62e124c1342154608423ec3fa8aa9537\transformed\exoplayer-dash-2.18.5\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.5] C:\gradle-home\caches\8.14.1\transforms\d0eaec1274a8fa4a7c650bdb89b843d5\transformed\exoplayer-hls-2.18.5\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.5] C:\gradle-home\caches\8.14.1\transforms\f95e155e4552234932aba133da9c21e5\transformed\exoplayer-smoothstreaming-2.18.5\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.5] C:\gradle-home\caches\8.14.1\transforms\4874790671c8ee4a83a51a3d7307ebb2\transformed\exoplayer-core-2.18.5\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\gradle-home\caches\8.14.1\transforms\d1a12f2d409f56f811111af3f906c66b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\gradle-home\caches\8.14.1\transforms\6b047dc5e5dd41cd0a5ae56055469ab1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\gradle-home\caches\8.14.1\transforms\c353433336c78773a7bb3f91c86ae1f8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\gradle-home\caches\8.14.1\transforms\5440da803a54d90a0e6bf83df3565713\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\gradle-home\caches\8.14.1\transforms\b64acb0b468941e11649dcc8c1a54ad4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\gradle-home\caches\8.14.1\transforms\feba8e6e6cb571b77da5663b0481e1fc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.5] C:\gradle-home\caches\8.14.1\transforms\660657241239aba9a58fa535da30e4bc\transformed\exoplayer-ui-2.18.5\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\gradle-home\caches\8.14.1\transforms\ad4a1a27345180cd2993e7506e7f3416\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.6.0] C:\gradle-home\caches\8.14.1\transforms\be2d4faa434f5fd45b18a8952ef333e2\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\gradle-home\caches\8.14.1\transforms\93262e33c26610f65e5352e9e4b7894c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\gradle-home\caches\8.14.1\transforms\e32dde231d0131b41cb8c0496a9c5806\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\gradle-home\caches\8.14.1\transforms\d752b2b668ebc9c30649147fefda68e2\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\gradle-home\caches\8.14.1\transforms\e6966a5e1a33e1c2576dce28f9969acb\transformed\savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\gradle-home\caches\8.14.1\transforms\febfb3e1a2c37f9ef97b61c722c7109d\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\gradle-home\caches\8.14.1\transforms\1551a9992dae9b27f9caef6f4ddbbf24\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\gradle-home\caches\8.14.1\transforms\dfb75d5b045c523145d6a8d2cd234d4b\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\gradle-home\caches\8.14.1\transforms\67b12e297f84e51286343328a2174603\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.5] C:\gradle-home\caches\8.14.1\transforms\1d4efda269fdafd644f53caee49db6b7\transformed\exoplayer-datasource-2.18.5\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.5] C:\gradle-home\caches\8.14.1\transforms\c9c46d539aaf6172494d408106c2a08d\transformed\exoplayer-extractor-2.18.5\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.5] C:\gradle-home\caches\8.14.1\transforms\e190e3c6255ca8106c26fe7beeb82c9a\transformed\exoplayer-decoder-2.18.5\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.5] C:\gradle-home\caches\8.14.1\transforms\b2c9f78e2e38b5c8bb2288ff0f7242d8\transformed\exoplayer-database-2.18.5\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.5] C:\gradle-home\caches\8.14.1\transforms\a57bafc6d9118ed85e2e483bbcc51db2\transformed\exoplayer-common-2.18.5\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\gradle-home\caches\8.14.1\transforms\fbff880f7555c869f000a3ec789a2fe7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\gradle-home\caches\8.14.1\transforms\d4fb432325fb51a2238313d03fa440c7\transformed\gifdecoder-4.14.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\gradle-home\caches\8.14.1\transforms\3066c5de00afa73a9f64f42ae3465696\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\gradle-home\caches\8.14.1\transforms\4861bbf29016e0dccc0bf79af1c955b9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\gradle-home\caches\8.14.1\transforms\532df5955e04fe2cde4e4bc087a91c5c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\gradle-home\caches\8.14.1\transforms\3b35c9fadec24b973e9c8b088c396e18\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\gradle-home\caches\8.14.1\transforms\aa2373fdb9822c4b781c212c5b39e29d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\gradle-home\caches\8.14.1\transforms\4535627d01af275fe5638da0c4b68410\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\gradle-home\caches\8.14.1\transforms\24d3a7b199d0a123cd450339df0159c6\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\gradle-home\caches\8.14.1\transforms\d648ea409acde0e4fd5d63cc30eb4039\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\gradle-home\caches\8.14.1\transforms\7a0acd48a603f9b3e89ddcd600d529b6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\gradle-home\caches\8.14.1\transforms\9ff8f7eaa4ba78461035b107a6c2b341\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:3:5-38
		INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:5:5-80
	android:name
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:5:22-77
uses-permission#android.permission.INTERNET
ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:6:22-64
application
ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:8:5-23:19
INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml:8:5-23:19
MERGED from [com.google.android.material:material:1.8.0] C:\gradle-home\caches\8.14.1\transforms\473d6f74a7df0a9331ff7f3743537b94\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.8.0] C:\gradle-home\caches\8.14.1\transforms\473d6f74a7df0a9331ff7f3743537b94\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\gradle-home\caches\8.14.1\transforms\6558c962b139a4ad878360b49ac0923b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\gradle-home\caches\8.14.1\transforms\6558c962b139a4ad878360b49ac0923b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\gradle-home\caches\8.14.1\transforms\9f87f88ae7c8ae3fa78b0eece36ed6e8\transformed\glide-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\gradle-home\caches\8.14.1\transforms\9f87f88ae7c8ae3fa78b0eece36ed6e8\transformed\glide-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\gradle-home\caches\8.14.1\transforms\fbff880f7555c869f000a3ec789a2fe7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\gradle-home\caches\8.14.1\transforms\fbff880f7555c869f000a3ec789a2fe7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\gradle-home\caches\8.14.1\transforms\d4fb432325fb51a2238313d03fa440c7\transformed\gifdecoder-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\gradle-home\caches\8.14.1\transforms\d4fb432325fb51a2238313d03fa440c7\transformed\gifdecoder-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\gradle-home\caches\8.14.1\transforms\4861bbf29016e0dccc0bf79af1c955b9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\gradle-home\caches\8.14.1\transforms\4861bbf29016e0dccc0bf79af1c955b9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:11:9-41
	android:roundIcon
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:12:9-54
	android:icon
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:10:9-43
	android:allowBackup
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:9:9-35
	android:theme
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:14:9-49
activity#com.example.videoplayer.MainActivity
ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:15:9-22:20
	android:exported
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:17:13-36
	android:name
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:18:13-21:29
action#android.intent.action.MAIN
ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:19:17-69
	android:name
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:19:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:20:17-77
	android:name
		ADDED from E:\kkkk\app\src\main\AndroidManifest.xml:20:27-74
uses-sdk
INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml
INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.8.0] C:\gradle-home\caches\8.14.1\transforms\473d6f74a7df0a9331ff7f3743537b94\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.8.0] C:\gradle-home\caches\8.14.1\transforms\473d6f74a7df0a9331ff7f3743537b94\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\gradle-home\caches\8.14.1\transforms\6558c962b139a4ad878360b49ac0923b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\gradle-home\caches\8.14.1\transforms\6558c962b139a4ad878360b49ac0923b\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\gradle-home\caches\8.14.1\transforms\bb3ab7ebc5cfac50f3aa0218b7126221\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\gradle-home\caches\8.14.1\transforms\bb3ab7ebc5cfac50f3aa0218b7126221\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\gradle-home\caches\8.14.1\transforms\b0cee708ffbac1b04351a1bcae21fc1d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\gradle-home\caches\8.14.1\transforms\b0cee708ffbac1b04351a1bcae21fc1d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\gradle-home\caches\8.14.1\transforms\a6905b79b4f131661046404be177249c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\gradle-home\caches\8.14.1\transforms\a6905b79b4f131661046404be177249c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\gradle-home\caches\8.14.1\transforms\9f87f88ae7c8ae3fa78b0eece36ed6e8\transformed\glide-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\gradle-home\caches\8.14.1\transforms\9f87f88ae7c8ae3fa78b0eece36ed6e8\transformed\glide-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\gradle-home\caches\8.14.1\transforms\2ef9756dc4712abd1352b8b267772b82\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\gradle-home\caches\8.14.1\transforms\2ef9756dc4712abd1352b8b267772b82\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\gradle-home\caches\8.14.1\transforms\b61cef457b792e3299c0090c433369fc\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\gradle-home\caches\8.14.1\transforms\b61cef457b792e3299c0090c433369fc\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\gradle-home\caches\8.14.1\transforms\11471678fcc1a3ff3a05cb52ee358cb9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\gradle-home\caches\8.14.1\transforms\11471678fcc1a3ff3a05cb52ee358cb9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\gradle-home\caches\8.14.1\transforms\f5684cc501bccec97e63bf765b7dbb60\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\gradle-home\caches\8.14.1\transforms\f5684cc501bccec97e63bf765b7dbb60\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\gradle-home\caches\8.14.1\transforms\e9e7ec9dd0ecbcd7a4bcbae7a80106bc\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\gradle-home\caches\8.14.1\transforms\e9e7ec9dd0ecbcd7a4bcbae7a80106bc\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\gradle-home\caches\8.14.1\transforms\a2db37f316450a14ac9b67e261a4ffca\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\gradle-home\caches\8.14.1\transforms\a2db37f316450a14ac9b67e261a4ffca\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\gradle-home\caches\8.14.1\transforms\b9ac6a1a1737e81aff50b802b7906914\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\gradle-home\caches\8.14.1\transforms\b9ac6a1a1737e81aff50b802b7906914\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.5] C:\gradle-home\caches\8.14.1\transforms\62e124c1342154608423ec3fa8aa9537\transformed\exoplayer-dash-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.5] C:\gradle-home\caches\8.14.1\transforms\62e124c1342154608423ec3fa8aa9537\transformed\exoplayer-dash-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.5] C:\gradle-home\caches\8.14.1\transforms\d0eaec1274a8fa4a7c650bdb89b843d5\transformed\exoplayer-hls-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.5] C:\gradle-home\caches\8.14.1\transforms\d0eaec1274a8fa4a7c650bdb89b843d5\transformed\exoplayer-hls-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.5] C:\gradle-home\caches\8.14.1\transforms\f95e155e4552234932aba133da9c21e5\transformed\exoplayer-smoothstreaming-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.5] C:\gradle-home\caches\8.14.1\transforms\f95e155e4552234932aba133da9c21e5\transformed\exoplayer-smoothstreaming-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.5] C:\gradle-home\caches\8.14.1\transforms\4874790671c8ee4a83a51a3d7307ebb2\transformed\exoplayer-core-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.5] C:\gradle-home\caches\8.14.1\transforms\4874790671c8ee4a83a51a3d7307ebb2\transformed\exoplayer-core-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\gradle-home\caches\8.14.1\transforms\d1a12f2d409f56f811111af3f906c66b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\gradle-home\caches\8.14.1\transforms\d1a12f2d409f56f811111af3f906c66b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\gradle-home\caches\8.14.1\transforms\6b047dc5e5dd41cd0a5ae56055469ab1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\gradle-home\caches\8.14.1\transforms\6b047dc5e5dd41cd0a5ae56055469ab1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\gradle-home\caches\8.14.1\transforms\c353433336c78773a7bb3f91c86ae1f8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\gradle-home\caches\8.14.1\transforms\c353433336c78773a7bb3f91c86ae1f8\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\gradle-home\caches\8.14.1\transforms\5440da803a54d90a0e6bf83df3565713\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\gradle-home\caches\8.14.1\transforms\5440da803a54d90a0e6bf83df3565713\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\gradle-home\caches\8.14.1\transforms\b64acb0b468941e11649dcc8c1a54ad4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\gradle-home\caches\8.14.1\transforms\b64acb0b468941e11649dcc8c1a54ad4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\gradle-home\caches\8.14.1\transforms\feba8e6e6cb571b77da5663b0481e1fc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\gradle-home\caches\8.14.1\transforms\feba8e6e6cb571b77da5663b0481e1fc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.5] C:\gradle-home\caches\8.14.1\transforms\660657241239aba9a58fa535da30e4bc\transformed\exoplayer-ui-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.5] C:\gradle-home\caches\8.14.1\transforms\660657241239aba9a58fa535da30e4bc\transformed\exoplayer-ui-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\gradle-home\caches\8.14.1\transforms\ad4a1a27345180cd2993e7506e7f3416\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\gradle-home\caches\8.14.1\transforms\ad4a1a27345180cd2993e7506e7f3416\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\gradle-home\caches\8.14.1\transforms\be2d4faa434f5fd45b18a8952ef333e2\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\gradle-home\caches\8.14.1\transforms\be2d4faa434f5fd45b18a8952ef333e2\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\gradle-home\caches\8.14.1\transforms\93262e33c26610f65e5352e9e4b7894c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\gradle-home\caches\8.14.1\transforms\93262e33c26610f65e5352e9e4b7894c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\gradle-home\caches\8.14.1\transforms\e32dde231d0131b41cb8c0496a9c5806\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\gradle-home\caches\8.14.1\transforms\e32dde231d0131b41cb8c0496a9c5806\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\gradle-home\caches\8.14.1\transforms\d752b2b668ebc9c30649147fefda68e2\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\gradle-home\caches\8.14.1\transforms\d752b2b668ebc9c30649147fefda68e2\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\gradle-home\caches\8.14.1\transforms\e6966a5e1a33e1c2576dce28f9969acb\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\gradle-home\caches\8.14.1\transforms\e6966a5e1a33e1c2576dce28f9969acb\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\gradle-home\caches\8.14.1\transforms\febfb3e1a2c37f9ef97b61c722c7109d\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\gradle-home\caches\8.14.1\transforms\febfb3e1a2c37f9ef97b61c722c7109d\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\gradle-home\caches\8.14.1\transforms\1551a9992dae9b27f9caef6f4ddbbf24\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\gradle-home\caches\8.14.1\transforms\1551a9992dae9b27f9caef6f4ddbbf24\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\gradle-home\caches\8.14.1\transforms\dfb75d5b045c523145d6a8d2cd234d4b\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\gradle-home\caches\8.14.1\transforms\dfb75d5b045c523145d6a8d2cd234d4b\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\gradle-home\caches\8.14.1\transforms\67b12e297f84e51286343328a2174603\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\gradle-home\caches\8.14.1\transforms\67b12e297f84e51286343328a2174603\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.5] C:\gradle-home\caches\8.14.1\transforms\1d4efda269fdafd644f53caee49db6b7\transformed\exoplayer-datasource-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.5] C:\gradle-home\caches\8.14.1\transforms\1d4efda269fdafd644f53caee49db6b7\transformed\exoplayer-datasource-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.5] C:\gradle-home\caches\8.14.1\transforms\c9c46d539aaf6172494d408106c2a08d\transformed\exoplayer-extractor-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.5] C:\gradle-home\caches\8.14.1\transforms\c9c46d539aaf6172494d408106c2a08d\transformed\exoplayer-extractor-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.5] C:\gradle-home\caches\8.14.1\transforms\e190e3c6255ca8106c26fe7beeb82c9a\transformed\exoplayer-decoder-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.5] C:\gradle-home\caches\8.14.1\transforms\e190e3c6255ca8106c26fe7beeb82c9a\transformed\exoplayer-decoder-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.5] C:\gradle-home\caches\8.14.1\transforms\b2c9f78e2e38b5c8bb2288ff0f7242d8\transformed\exoplayer-database-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.5] C:\gradle-home\caches\8.14.1\transforms\b2c9f78e2e38b5c8bb2288ff0f7242d8\transformed\exoplayer-database-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.5] C:\gradle-home\caches\8.14.1\transforms\a57bafc6d9118ed85e2e483bbcc51db2\transformed\exoplayer-common-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.5] C:\gradle-home\caches\8.14.1\transforms\a57bafc6d9118ed85e2e483bbcc51db2\transformed\exoplayer-common-2.18.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\gradle-home\caches\8.14.1\transforms\fbff880f7555c869f000a3ec789a2fe7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\gradle-home\caches\8.14.1\transforms\fbff880f7555c869f000a3ec789a2fe7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\gradle-home\caches\8.14.1\transforms\d4fb432325fb51a2238313d03fa440c7\transformed\gifdecoder-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\gradle-home\caches\8.14.1\transforms\d4fb432325fb51a2238313d03fa440c7\transformed\gifdecoder-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\gradle-home\caches\8.14.1\transforms\3066c5de00afa73a9f64f42ae3465696\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\gradle-home\caches\8.14.1\transforms\3066c5de00afa73a9f64f42ae3465696\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\gradle-home\caches\8.14.1\transforms\4861bbf29016e0dccc0bf79af1c955b9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\gradle-home\caches\8.14.1\transforms\4861bbf29016e0dccc0bf79af1c955b9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\gradle-home\caches\8.14.1\transforms\532df5955e04fe2cde4e4bc087a91c5c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\gradle-home\caches\8.14.1\transforms\532df5955e04fe2cde4e4bc087a91c5c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\gradle-home\caches\8.14.1\transforms\3b35c9fadec24b973e9c8b088c396e18\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\gradle-home\caches\8.14.1\transforms\3b35c9fadec24b973e9c8b088c396e18\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\gradle-home\caches\8.14.1\transforms\aa2373fdb9822c4b781c212c5b39e29d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\gradle-home\caches\8.14.1\transforms\aa2373fdb9822c4b781c212c5b39e29d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\gradle-home\caches\8.14.1\transforms\4535627d01af275fe5638da0c4b68410\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\gradle-home\caches\8.14.1\transforms\4535627d01af275fe5638da0c4b68410\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\gradle-home\caches\8.14.1\transforms\24d3a7b199d0a123cd450339df0159c6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\gradle-home\caches\8.14.1\transforms\24d3a7b199d0a123cd450339df0159c6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\gradle-home\caches\8.14.1\transforms\d648ea409acde0e4fd5d63cc30eb4039\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\gradle-home\caches\8.14.1\transforms\d648ea409acde0e4fd5d63cc30eb4039\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\gradle-home\caches\8.14.1\transforms\7a0acd48a603f9b3e89ddcd600d529b6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\gradle-home\caches\8.14.1\transforms\7a0acd48a603f9b3e89ddcd600d529b6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\gradle-home\caches\8.14.1\transforms\9ff8f7eaa4ba78461035b107a6c2b341\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\gradle-home\caches\8.14.1\transforms\9ff8f7eaa4ba78461035b107a6c2b341\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\kkkk\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.exoplayer:exoplayer-core:2.18.5] C:\gradle-home\caches\8.14.1\transforms\4874790671c8ee4a83a51a3d7307ebb2\transformed\exoplayer-core-2.18.5\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.5] C:\gradle-home\caches\8.14.1\transforms\a57bafc6d9118ed85e2e483bbcc51db2\transformed\exoplayer-common-2.18.5\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.5] C:\gradle-home\caches\8.14.1\transforms\a57bafc6d9118ed85e2e483bbcc51db2\transformed\exoplayer-common-2.18.5\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [com.google.android.exoplayer:exoplayer-core:2.18.5] C:\gradle-home\caches\8.14.1\transforms\4874790671c8ee4a83a51a3d7307ebb2\transformed\exoplayer-core-2.18.5\AndroidManifest.xml:24:22-76
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\gradle-home\caches\8.14.1\transforms\4861bbf29016e0dccc0bf79af1c955b9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\gradle-home\caches\8.14.1\transforms\4861bbf29016e0dccc0bf79af1c955b9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.example.videoplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.videoplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
