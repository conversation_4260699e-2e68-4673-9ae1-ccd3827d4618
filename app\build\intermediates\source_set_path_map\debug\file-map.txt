com.example.videoplayer.app-lifecycle-process-2.4.1-0 C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\res
com.example.videoplayer.app-fragment-1.3.6-1 C:\gradle-home\caches\8.14.1\transforms\2ef9756dc4712abd1352b8b267772b82\transformed\fragment-1.3.6\res
com.example.videoplayer.app-core-1.9.0-2 C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\res
com.example.videoplayer.app-material-1.8.0-3 C:\gradle-home\caches\8.14.1\transforms\473d6f74a7df0a9331ff7f3743537b94\transformed\material-1.8.0\res
com.example.videoplayer.app-startup-runtime-1.1.1-4 C:\gradle-home\caches\8.14.1\transforms\4861bbf29016e0dccc0bf79af1c955b9\transformed\startup-runtime-1.1.1\res
com.example.videoplayer.app-exoplayer-core-2.18.5-5 C:\gradle-home\caches\8.14.1\transforms\4874790671c8ee4a83a51a3d7307ebb2\transformed\exoplayer-core-2.18.5\res
com.example.videoplayer.app-transition-1.2.0-6 C:\gradle-home\caches\8.14.1\transforms\5440da803a54d90a0e6bf83df3565713\transformed\transition-1.2.0\res
com.example.videoplayer.app-constraintlayout-2.1.4-7 C:\gradle-home\caches\8.14.1\transforms\6558c962b139a4ad878360b49ac0923b\transformed\constraintlayout-2.1.4\res
com.example.videoplayer.app-exoplayer-ui-2.18.5-8 C:\gradle-home\caches\8.14.1\transforms\660657241239aba9a58fa535da30e4bc\transformed\exoplayer-ui-2.18.5\res
com.example.videoplayer.app-cardview-1.0.0-9 C:\gradle-home\caches\8.14.1\transforms\67b12e297f84e51286343328a2174603\transformed\cardview-1.0.0\res
com.example.videoplayer.app-drawerlayout-1.1.1-10 C:\gradle-home\caches\8.14.1\transforms\6b047dc5e5dd41cd0a5ae56055469ab1\transformed\drawerlayout-1.1.1\res
com.example.videoplayer.app-emoji2-1.2.0-11 C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\res
com.example.videoplayer.app-glide-4.14.2-12 C:\gradle-home\caches\8.14.1\transforms\9f87f88ae7c8ae3fa78b0eece36ed6e8\transformed\glide-4.14.2\res
com.example.videoplayer.app-lifecycle-viewmodel-2.5.1-13 C:\gradle-home\caches\8.14.1\transforms\a2db37f316450a14ac9b67e261a4ffca\transformed\lifecycle-viewmodel-2.5.1\res
com.example.videoplayer.app-viewpager2-1.0.0-14 C:\gradle-home\caches\8.14.1\transforms\a6905b79b4f131661046404be177249c\transformed\viewpager2-1.0.0\res
com.example.videoplayer.app-lifecycle-livedata-core-2.5.1-15 C:\gradle-home\caches\8.14.1\transforms\aa2373fdb9822c4b781c212c5b39e29d\transformed\lifecycle-livedata-core-2.5.1\res
com.example.videoplayer.app-recyclerview-1.2.1-16 C:\gradle-home\caches\8.14.1\transforms\ad4a1a27345180cd2993e7506e7f3416\transformed\recyclerview-1.2.1\res
com.example.videoplayer.app-appcompat-1.6.1-17 C:\gradle-home\caches\8.14.1\transforms\b0cee708ffbac1b04351a1bcae21fc1d\transformed\appcompat-1.6.1\res
com.example.videoplayer.app-activity-1.6.0-18 C:\gradle-home\caches\8.14.1\transforms\b61cef457b792e3299c0090c433369fc\transformed\activity-1.6.0\res
com.example.videoplayer.app-lifecycle-viewmodel-savedstate-2.5.1-19 C:\gradle-home\caches\8.14.1\transforms\b9ac6a1a1737e81aff50b802b7906914\transformed\lifecycle-viewmodel-savedstate-2.5.1\res
com.example.videoplayer.app-appcompat-resources-1.6.1-20 C:\gradle-home\caches\8.14.1\transforms\bb3ab7ebc5cfac50f3aa0218b7126221\transformed\appcompat-resources-1.6.1\res
com.example.videoplayer.app-media-1.6.0-21 C:\gradle-home\caches\8.14.1\transforms\be2d4faa434f5fd45b18a8952ef333e2\transformed\media-1.6.0\res
com.example.videoplayer.app-coordinatorlayout-1.1.0-22 C:\gradle-home\caches\8.14.1\transforms\c353433336c78773a7bb3f91c86ae1f8\transformed\coordinatorlayout-1.1.0\res
com.example.videoplayer.app-emoji2-views-helper-1.2.0-23 C:\gradle-home\caches\8.14.1\transforms\d1a12f2d409f56f811111af3f906c66b\transformed\emoji2-views-helper-1.2.0\res
com.example.videoplayer.app-core-ktx-1.9.0-24 C:\gradle-home\caches\8.14.1\transforms\d752b2b668ebc9c30649147fefda68e2\transformed\core-ktx-1.9.0\res
com.example.videoplayer.app-lifecycle-runtime-2.5.1-25 C:\gradle-home\caches\8.14.1\transforms\dfb75d5b045c523145d6a8d2cd234d4b\transformed\lifecycle-runtime-2.5.1\res
com.example.videoplayer.app-savedstate-1.2.0-26 C:\gradle-home\caches\8.14.1\transforms\e6966a5e1a33e1c2576dce28f9969acb\transformed\savedstate-1.2.0\res
com.example.videoplayer.app-annotation-experimental-1.3.0-27 C:\gradle-home\caches\8.14.1\transforms\febfb3e1a2c37f9ef97b61c722c7109d\transformed\annotation-experimental-1.3.0\res
com.example.videoplayer.app-pngs-28 E:\kkkk\app\build\generated\res\pngs\debug
com.example.videoplayer.app-resValues-29 E:\kkkk\app\build\generated\res\resValues\debug
com.example.videoplayer.app-packageDebugResources-30 E:\kkkk\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.videoplayer.app-packageDebugResources-31 E:\kkkk\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.videoplayer.app-merged_res-32 E:\kkkk\app\build\intermediates\merged_res\debug
com.example.videoplayer.app-debug-33 E:\kkkk\app\src\debug\res
com.example.videoplayer.app-main-34 E:\kkkk\app\src\main\res
