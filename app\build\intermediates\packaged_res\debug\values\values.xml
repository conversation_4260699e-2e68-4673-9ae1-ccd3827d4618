<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background">#121212</color>
    <color name="error">#CF6679</color>
    <color name="on_background">#FFFFFF</color>
    <color name="on_error">#000000</color>
    <color name="on_primary">#FFFFFF</color>
    <color name="on_secondary">#000000</color>
    <color name="on_surface">#FFFFFF</color>
    <color name="primary">#FF6200EE</color>
    <color name="primary_dark">#FF3700B3</color>
    <color name="primary_variant">#FF3700B3</color>
    <color name="secondary">#FF03DAC5</color>
    <color name="secondary_variant">#FF018786</color>
    <color name="surface">#1E1E1E</color>
    <color name="video_item_background">#2D2D2D</color>
    <color name="video_item_subtext">#B3FFFFFF</color>
    <color name="video_item_text">#FFFFFF</color>
    <string name="add_to_favorites">إضافة إلى المفضلة</string>
    <string name="all_videos">كل الفيديوهات</string>
    <string name="app_name">مشغل الفيديو</string>
    <string name="favorites">المفضلة</string>
    <string name="no_videos_found">لم يتم العثور على فيديوهات</string>
    <string name="permission_denied">تم رفض الإذن. لا يمكن تحميل الفيديوهات.</string>
    <string name="recently_played">تم تشغيله مؤخراً</string>
    <string name="remove_from_favorites">إزالة من المفضلة</string>
    <string name="select_video">اختر فيديو</string>
    <string name="video_details">%1$s • %2$s • %3$s</string>
    <style name="Theme.VideoPlayer" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/on_error</item>
        <item name="android:statusBarColor">@color/primary_dark</item>
    </style>
</resources>