1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.videoplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->E:\kkkk\app\src\main\AndroidManifest.xml:5:5-80
11-->E:\kkkk\app\src\main\AndroidManifest.xml:5:22-77
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\kkkk\app\src\main\AndroidManifest.xml:6:5-67
12-->E:\kkkk\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->[com.google.android.exoplayer:exoplayer-core:2.18.5] C:\gradle-home\caches\8.14.1\transforms\4874790671c8ee4a83a51a3d7307ebb2\transformed\exoplayer-core-2.18.5\AndroidManifest.xml:24:5-79
13-->[com.google.android.exoplayer:exoplayer-core:2.18.5] C:\gradle-home\caches\8.14.1\transforms\4874790671c8ee4a83a51a3d7307ebb2\transformed\exoplayer-core-2.18.5\AndroidManifest.xml:24:22-76
14
15    <permission
15-->[androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.videoplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.videoplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->E:\kkkk\app\src\main\AndroidManifest.xml:8:5-23:19
22        android:allowBackup="true"
22-->E:\kkkk\app\src\main\AndroidManifest.xml:9:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.9.0] C:\gradle-home\caches\8.14.1\transforms\42c9cce8c7b5fed18c278c11b515454e\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
24        android:debuggable="true"
25        android:extractNativeLibs="true"
26        android:icon="@mipmap/ic_launcher"
26-->E:\kkkk\app\src\main\AndroidManifest.xml:10:9-43
27        android:label="@string/app_name"
27-->E:\kkkk\app\src\main\AndroidManifest.xml:11:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->E:\kkkk\app\src\main\AndroidManifest.xml:12:9-54
29        android:supportsRtl="true"
29-->E:\kkkk\app\src\main\AndroidManifest.xml:13:9-35
30        android:theme="@style/Theme.VideoPlayer" >
30-->E:\kkkk\app\src\main\AndroidManifest.xml:14:9-49
31        <activity
31-->E:\kkkk\app\src\main\AndroidManifest.xml:15:9-22:20
32            android:name="com.example.videoplayer.MainActivity"
32-->E:\kkkk\app\src\main\AndroidManifest.xml:16:13-41
33            android:exported="true" >
33-->E:\kkkk\app\src\main\AndroidManifest.xml:17:13-36
34            <intent-filter>
34-->E:\kkkk\app\src\main\AndroidManifest.xml:18:13-21:29
35                <action android:name="android.intent.action.MAIN" />
35-->E:\kkkk\app\src\main\AndroidManifest.xml:19:17-69
35-->E:\kkkk\app\src\main\AndroidManifest.xml:19:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->E:\kkkk\app\src\main\AndroidManifest.xml:20:17-77
37-->E:\kkkk\app\src\main\AndroidManifest.xml:20:27-74
38            </intent-filter>
39        </activity>
40
41        <provider
41-->[androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
42            android:name="androidx.startup.InitializationProvider"
42-->[androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
43            android:authorities="com.example.videoplayer.androidx-startup"
43-->[androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
44            android:exported="false" >
44-->[androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
45            <meta-data
45-->[androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
46                android:name="androidx.emoji2.text.EmojiCompatInitializer"
46-->[androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
47                android:value="androidx.startup" />
47-->[androidx.emoji2:emoji2:1.2.0] C:\gradle-home\caches\8.14.1\transforms\70c417a48581d48fafe391640527c84b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
48            <meta-data
48-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
49                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
49-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
50                android:value="androidx.startup" />
50-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\gradle-home\caches\8.14.1\transforms\007d405cd2d75ec0101056f6de245489\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
51        </provider>
52    </application>
53
54</manifest>
