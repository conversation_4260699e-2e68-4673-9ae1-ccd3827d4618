{"logs": [{"outputFile": "com.example.videoplayer.app-mergeDebugResources-30:/values-cs/values-cs.xml", "map": [{"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\b0cee708ffbac1b04351a1bcae21fc1d\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1287,1394,1496,1606,1692,1797,1914,1992,2068,2159,2252,2347,2441,2535,2628,2723,2820,2911,3002,3086,3190,3302,3401,3507,3618,3720,3883,12677", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "1389,1491,1601,1687,1792,1909,1987,2063,2154,2247,2342,2436,2530,2623,2718,2815,2906,2997,3081,3185,3297,3396,3502,3613,3715,3878,3976,12755"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\4874790671c8ee4a83a51a3d7307ebb2\\transformed\\exoplayer-core-2.18.5\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6787,6863,6925,6988,7057,7134,7204,7286,7366", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "6858,6920,6983,7052,7129,7199,7281,7361,7441"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\660657241239aba9a58fa535da30e4bc\\transformed\\exoplayer-ui-2.18.5\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2072,2187,2310,2384,2464,2536,2617,2711,2806,2873,2938,2991,3049,3097,3158,3224,3291,3354,3421,3486,3545,3610,3674,3740,3792,3855,3932,4009", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2067,2182,2305,2379,2459,2531,2612,2706,2801,2868,2933,2986,3044,3092,3153,3219,3286,3349,3416,3481,3540,3605,3669,3735,3787,3850,3927,4004,4058"}, "to": {"startLines": "2,11,17,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,705,4773,4854,4934,5012,5114,5212,5290,5354,5443,5535,5605,5671,5736,5808,5921,6036,6159,6233,6313,6385,6466,6560,6655,6722,7446,7499,7557,7605,7666,7732,7799,7862,7929,7994,8053,8118,8182,8248,8300,8363,8440,8517", "endLines": "10,16,22,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "377,700,1014,4849,4929,5007,5109,5207,5285,5349,5438,5530,5600,5666,5731,5803,5916,6031,6154,6228,6308,6380,6461,6555,6650,6717,6782,7494,7552,7600,7661,7727,7794,7857,7924,7989,8048,8113,8177,8243,8295,8358,8435,8512,8566"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\42c9cce8c7b5fed18c278c11b515454e\\transformed\\core-1.9.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "170", "startColumns": "4", "startOffsets": "12760", "endColumns": "100", "endOffsets": "12856"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\473d6f74a7df0a9331ff7f3743537b94\\transformed\\material-1.8.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1160,1259,1335,1396,1485,1549,1616,1670,1738,1798,1852,1969,2029,2091,2145,2217,2339,2423,2515,2652,2730,2812,2900,2954,3005,3071,3143,3220,3304,3376,3453,3527,3598,3686,3757,3850,3945,4019,4093,4189,4241,4308,4394,4482,4544,4608,4671,4781,4877,4976,5074,5132,5187", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,87,53,50,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,57,54,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1155,1254,1330,1391,1480,1544,1611,1665,1733,1793,1847,1964,2024,2086,2140,2212,2334,2418,2510,2647,2725,2807,2895,2949,3000,3066,3138,3215,3299,3371,3448,3522,3593,3681,3752,3845,3940,4014,4088,4184,4236,4303,4389,4477,4539,4603,4666,4776,4872,4971,5069,5127,5182,5261"}, "to": {"startLines": "23,56,57,58,59,60,61,62,63,64,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1019,3981,4059,4137,4214,4317,4409,4501,4627,4708,8571,8670,8746,8807,8896,8960,9027,9081,9149,9209,9263,9380,9440,9502,9556,9628,9750,9834,9926,10063,10141,10223,10311,10365,10416,10482,10554,10631,10715,10787,10864,10938,11009,11097,11168,11261,11356,11430,11504,11600,11652,11719,11805,11893,11955,12019,12082,12192,12288,12387,12485,12543,12598", "endLines": "28,56,57,58,59,60,61,62,63,64,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,87,53,50,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,57,54,78", "endOffsets": "1282,4054,4132,4209,4312,4404,4496,4622,4703,4768,8665,8741,8802,8891,8955,9022,9076,9144,9204,9258,9375,9435,9497,9551,9623,9745,9829,9921,10058,10136,10218,10306,10360,10411,10477,10549,10626,10710,10782,10859,10933,11004,11092,11163,11256,11351,11425,11499,11595,11647,11714,11800,11888,11950,12014,12077,12187,12283,12382,12480,12538,12593,12672"}}]}]}