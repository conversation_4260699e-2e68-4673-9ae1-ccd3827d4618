{"logs": [{"outputFile": "com.example.videoplayer.app-mergeDebugResources-30:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\b0cee708ffbac1b04351a1bcae21fc1d\\transformed\\appcompat-1.6.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "706,810,910,1018,1102,1202,1317,1395,1470,1561,1654,1749,1843,1943,2036,2131,2225,2316,2407,2489,2592,2695,2794,2899,3003,3107,3263,7027", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "805,905,1013,1097,1197,1312,1390,1465,1556,1649,1744,1838,1938,2031,2126,2220,2311,2402,2484,2587,2690,2789,2894,2998,3102,3258,3358,7105"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\42c9cce8c7b5fed18c278c11b515454e\\transformed\\core-1.9.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "7110", "endColumns": "100", "endOffsets": "7206"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\4874790671c8ee4a83a51a3d7307ebb2\\transformed\\exoplayer-core-2.18.5\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5265,5335,5397,5462,5526,5603,5668,5758,5843", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "5330,5392,5457,5521,5598,5663,5753,5838,5907"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\660657241239aba9a58fa535da30e4bc\\transformed\\exoplayer-ui-2.18.5\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1755,1866,1976,2043,2123,2194,2261,2346,2431,2494,2558,2611,2669,2717,2778,2843,2905,2970,3041,3099,3157,3223,3287,3353,3405,3467,3543,3619", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1750,1861,1971,2038,2118,2189,2256,2341,2426,2489,2553,2606,2664,2712,2773,2838,2900,2965,3036,3094,3152,3218,3282,3348,3400,3462,3538,3614,3668"}, "to": {"startLines": "2,11,15,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,529,3363,3445,3527,3605,3692,3777,3844,3907,3999,4091,4156,4219,4281,4352,4462,4573,4683,4750,4830,4901,4968,5053,5138,5201,5912,5965,6023,6071,6132,6197,6259,6324,6395,6453,6511,6577,6641,6707,6759,6821,6897,6973", "endLines": "10,14,18,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "330,524,701,3440,3522,3600,3687,3772,3839,3902,3994,4086,4151,4214,4276,4347,4457,4568,4678,4745,4825,4896,4963,5048,5133,5196,5260,5960,6018,6066,6127,6192,6254,6319,6390,6448,6506,6572,6636,6702,6754,6816,6892,6968,7022"}}]}]}