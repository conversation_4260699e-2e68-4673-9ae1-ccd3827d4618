{"logs": [{"outputFile": "com.example.videoplayer.app-mergeDebugResources-30:/values-mk/values-mk.xml", "map": [{"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\660657241239aba9a58fa535da30e4bc\\transformed\\exoplayer-ui-2.18.5\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,702,791,881,962,1052,1143,1221,1286,1389,1494,1559,1623,1686,1758,1876,1992,2107,2184,2273,2344,2423,2513,2604,2668,2736,2789,2847,2895,2956,3022,3089,3152,3222,3286,3344,3410,3475,3541,3593,3658,3737,3816", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "306,511,697,786,876,957,1047,1138,1216,1281,1384,1489,1554,1618,1681,1753,1871,1987,2102,2179,2268,2339,2418,2508,2599,2663,2731,2784,2842,2890,2951,3017,3084,3147,3217,3281,3339,3405,3470,3536,3588,3653,3732,3811,3867"}, "to": {"startLines": "2,11,15,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,406,611,4485,4574,4664,4745,4835,4926,5004,5069,5172,5277,5342,5406,5469,5541,5659,5775,5890,5967,6056,6127,6206,6296,6387,6451,7184,7237,7295,7343,7404,7470,7537,7600,7670,7734,7792,7858,7923,7989,8041,8106,8185,8264", "endLines": "10,14,18,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "17,12,12,88,89,80,89,90,77,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "401,606,792,4569,4659,4740,4830,4921,4999,5064,5167,5272,5337,5401,5464,5536,5654,5770,5885,5962,6051,6122,6201,6291,6382,6446,6514,7232,7290,7338,7399,7465,7532,7595,7665,7729,7787,7853,7918,7984,8036,8101,8180,8259,8315"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\42c9cce8c7b5fed18c278c11b515454e\\transformed\\core-1.9.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "164", "startColumns": "4", "startOffsets": "12590", "endColumns": "100", "endOffsets": "12686"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\4874790671c8ee4a83a51a3d7307ebb2\\transformed\\exoplayer-core-2.18.5\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,250,318,395,468,557,642", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "120,181,245,313,390,463,552,637,715"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6519,6589,6650,6714,6782,6859,6932,7021,7106", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "6584,6645,6709,6777,6854,6927,7016,7101,7179"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\473d6f74a7df0a9331ff7f3743537b94\\transformed\\material-1.8.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1073,1164,1234,1298,1401,1464,1529,1589,1657,1720,1775,1903,1960,2022,2077,2152,2292,2379,2462,2595,2677,2762,2849,2903,2958,3024,3097,3173,3262,3335,3411,3486,3556,3644,3719,3811,3903,3977,4051,4143,4196,4263,4346,4433,4495,4559,4622,4736,4843,4945,5056,5114,5173", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,86,53,54,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,57,58,84", "endOffsets": "266,347,427,509,606,695,791,915,1002,1068,1159,1229,1293,1396,1459,1524,1584,1652,1715,1770,1898,1955,2017,2072,2147,2287,2374,2457,2590,2672,2757,2844,2898,2953,3019,3092,3168,3257,3330,3406,3481,3551,3639,3714,3806,3898,3972,4046,4138,4191,4258,4341,4428,4490,4554,4617,4731,4838,4940,5051,5109,5168,5253"}, "to": {"startLines": "19,50,51,52,53,54,55,56,57,58,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "797,3683,3764,3844,3926,4023,4112,4208,4332,4419,8320,8411,8481,8545,8648,8711,8776,8836,8904,8967,9022,9150,9207,9269,9324,9399,9539,9626,9709,9842,9924,10009,10096,10150,10205,10271,10344,10420,10509,10582,10658,10733,10803,10891,10966,11058,11150,11224,11298,11390,11443,11510,11593,11680,11742,11806,11869,11983,12090,12192,12303,12361,12420", "endLines": "22,50,51,52,53,54,55,56,57,58,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162", "endColumns": "12,80,79,81,96,88,95,123,86,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,82,132,81,84,86,53,54,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,57,58,84", "endOffsets": "963,3759,3839,3921,4018,4107,4203,4327,4414,4480,8406,8476,8540,8643,8706,8771,8831,8899,8962,9017,9145,9202,9264,9319,9394,9534,9621,9704,9837,9919,10004,10091,10145,10200,10266,10339,10415,10504,10577,10653,10728,10798,10886,10961,11053,11145,11219,11293,11385,11438,11505,11588,11675,11737,11801,11864,11978,12085,12187,12298,12356,12415,12500"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\b0cee708ffbac1b04351a1bcae21fc1d\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "968,1076,1180,1288,1374,1482,1601,1685,1766,1857,1950,2046,2140,2240,2333,2428,2524,2615,2706,2793,2899,3005,3106,3213,3325,3429,3585,12505", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "1071,1175,1283,1369,1477,1596,1680,1761,1852,1945,2041,2135,2235,2328,2423,2519,2610,2701,2788,2894,3000,3101,3208,3320,3424,3580,3678,12585"}}]}]}