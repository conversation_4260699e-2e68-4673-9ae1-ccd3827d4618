<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\kkkk\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\kkkk\app\src\main\res"><file name="scale_down" path="E:\kkkk\app\src\main\res\anim\scale_down.xml" qualifiers="" type="anim"/><file name="scale_up" path="E:\kkkk\app\src\main\res\anim\scale_up.xml" qualifiers="" type="anim"/><file name="ic_favorite" path="E:\kkkk\app\src\main\res\drawable\ic_favorite.xml" qualifiers="" type="drawable"/><file name="ic_favorite_border" path="E:\kkkk\app\src\main\res\drawable\ic_favorite_border.xml" qualifiers="" type="drawable"/><file name="ic_video_placeholder" path="E:\kkkk\app\src\main\res\drawable\ic_video_placeholder.xml" qualifiers="" type="drawable"/><file name="ripple_effect" path="E:\kkkk\app\src\main\res\drawable\ripple_effect.xml" qualifiers="" type="drawable"/><file name="rounded_corner_background" path="E:\kkkk\app\src\main\res\drawable\rounded_corner_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="E:\kkkk\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_videos" path="E:\kkkk\app\src\main\res\layout\fragment_videos.xml" qualifiers="" type="layout"/><file name="item_video" path="E:\kkkk\app\src\main\res\layout\item_video.xml" qualifiers="" type="layout"/><file path="E:\kkkk\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary">#FF6200EE</color><color name="primary_dark">#FF3700B3</color><color name="primary_variant">#FF3700B3</color><color name="secondary">#FF03DAC5</color><color name="secondary_variant">#FF018786</color><color name="background">#121212</color><color name="surface">#1E1E1E</color><color name="error">#CF6679</color><color name="on_primary">#FFFFFF</color><color name="on_secondary">#000000</color><color name="on_background">#FFFFFF</color><color name="on_surface">#FFFFFF</color><color name="on_error">#000000</color><color name="video_item_background">#2D2D2D</color><color name="video_item_text">#FFFFFF</color><color name="video_item_subtext">#B3FFFFFF</color></file><file path="E:\kkkk\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">مشغل الفيديو</string><string name="select_video">اختر فيديو</string><string name="no_videos_found">لم يتم العثور على فيديوهات</string><string name="permission_denied">تم رفض الإذن. لا يمكن تحميل الفيديوهات.</string><string name="video_details">%1$s • %2$s • %3$s</string><string name="favorites">المفضلة</string><string name="all_videos">كل الفيديوهات</string><string name="recently_played">تم تشغيله مؤخراً</string><string name="add_to_favorites">إضافة إلى المفضلة</string><string name="remove_from_favorites">إزالة من المفضلة</string></file><file path="E:\kkkk\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.VideoPlayer" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/on_error</item>
        <item name="android:statusBarColor">@color/primary_dark</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\kkkk\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\kkkk\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\kkkk\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\kkkk\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>