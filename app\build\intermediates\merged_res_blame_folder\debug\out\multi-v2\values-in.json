{"logs": [{"outputFile": "com.example.videoplayer.app-mergeDebugResources-30:/values-in/values-in.xml", "map": [{"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\42c9cce8c7b5fed18c278c11b515454e\\transformed\\core-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "164", "startColumns": "4", "startOffsets": "12322", "endColumns": "100", "endOffsets": "12418"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\473d6f74a7df0a9331ff7f3743537b94\\transformed\\material-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1048,1142,1207,1266,1353,1415,1477,1537,1603,1665,1719,1831,1888,1949,2003,2075,2201,2287,2371,2510,2591,2672,2762,2815,2867,2933,3005,3089,3172,3247,3323,3396,3471,3556,3631,3723,3817,3891,3964,4058,4110,4179,4264,4351,4413,4477,4540,4643,4743,4838,4940,4997,5053", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "264,343,419,498,588,673,779,895,978,1043,1137,1202,1261,1348,1410,1472,1532,1598,1660,1714,1826,1883,1944,1998,2070,2196,2282,2366,2505,2586,2667,2757,2810,2862,2928,3000,3084,3167,3242,3318,3391,3466,3551,3626,3718,3812,3886,3959,4053,4105,4174,4259,4346,4408,4472,4535,4638,4738,4833,4935,4992,5048,5128"}, "to": {"startLines": "19,50,51,52,53,54,55,56,57,58,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "741,3614,3693,3769,3848,3938,4023,4129,4245,4328,8152,8246,8311,8370,8457,8519,8581,8641,8707,8769,8823,8935,8992,9053,9107,9179,9305,9391,9475,9614,9695,9776,9866,9919,9971,10037,10109,10193,10276,10351,10427,10500,10575,10660,10735,10827,10921,10995,11068,11162,11214,11283,11368,11455,11517,11581,11644,11747,11847,11942,12044,12101,12157", "endLines": "22,50,51,52,53,54,55,56,57,58,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "905,3688,3764,3843,3933,4018,4124,4240,4323,4388,8241,8306,8365,8452,8514,8576,8636,8702,8764,8818,8930,8987,9048,9102,9174,9300,9386,9470,9609,9690,9771,9861,9914,9966,10032,10104,10188,10271,10346,10422,10495,10570,10655,10730,10822,10916,10990,11063,11157,11209,11278,11363,11450,11512,11576,11639,11742,11842,11937,12039,12096,12152,12232"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\b0cee708ffbac1b04351a1bcae21fc1d\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "910,1025,1129,1234,1321,1425,1541,1624,1702,1793,1886,1981,2075,2175,2268,2363,2457,2548,2639,2725,2828,2933,3034,3138,3247,3355,3515,12237", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "1020,1124,1229,1316,1420,1536,1619,1697,1788,1881,1976,2070,2170,2263,2358,2452,2543,2634,2720,2823,2928,3029,3133,3242,3350,3510,3609,12317"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\4874790671c8ee4a83a51a3d7307ebb2\\transformed\\exoplayer-core-2.18.5\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6367,6435,6497,6562,6625,6701,6765,6865,6959", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "6430,6492,6557,6620,6696,6760,6860,6954,7023"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\660657241239aba9a58fa535da30e4bc\\transformed\\exoplayer-ui-2.18.5\\res\\values-in\\values-in.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,646,729,814,890,978,1071,1148,1217,1313,1407,1471,1535,1601,1674,1789,1907,2023,2095,2175,2245,2319,2403,2489,2556,2620,2673,2731,2779,2840,2904,2966,3029,3095,3157,3220,3286,3350,3416,3468,3530,3606,3682", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,724,809,885,973,1066,1143,1212,1308,1402,1466,1530,1596,1669,1784,1902,2018,2090,2170,2240,2314,2398,2484,2551,2615,2668,2726,2774,2835,2899,2961,3024,3090,3152,3215,3281,3345,3411,3463,3525,3601,3677,3739"}, "to": {"startLines": "2,11,15,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,567,4393,4476,4561,4637,4725,4818,4895,4964,5060,5154,5218,5282,5348,5421,5536,5654,5770,5842,5922,5992,6066,6150,6236,6303,7028,7081,7139,7187,7248,7312,7374,7437,7503,7565,7628,7694,7758,7824,7876,7938,8014,8090", "endLines": "10,14,18,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "375,562,736,4471,4556,4632,4720,4813,4890,4959,5055,5149,5213,5277,5343,5416,5531,5649,5765,5837,5917,5987,6061,6145,6231,6298,6362,7076,7134,7182,7243,7307,7369,7432,7498,7560,7623,7689,7753,7819,7871,7933,8009,8085,8147"}}]}]}