{"logs": [{"outputFile": "com.example.videoplayer.app-mergeDebugResources-30:/values-v21/values-v21.xml", "map": [{"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\b0cee708ffbac1b04351a1bcae21fc1d\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,36,38,39,40,41,43,45,46,47,48,49,51,53,55,57,59,61,62,67,69,71,72,73,75,77,78,79,80,85,97,140,143,186,201,213,215,217,219,222,226,229,230,231,234,235,236,237,238,239,242,243,245,247,249,251,255,257,258,259,260,262,266,268,270,271,272,273,274,275,311,312,313,323,324,325,337", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1662,1753,1856,1959,2064,2171,2280,2389,2498,2607,2716,2823,2926,3045,3200,3355,3460,3581,3682,3829,3970,4073,4192,4299,4402,4557,4728,4877,5042,5199,5350,5469,5820,5969,6118,6230,6377,6530,6677,6752,6841,6928,7453,8545,11303,11488,14258,15391,16243,16366,16489,16602,16785,17040,17241,17330,17441,17674,17775,17870,17993,18122,18239,18416,18515,18650,18793,18928,19047,19248,19367,19460,19571,19627,19734,19929,20040,20173,20268,20359,20450,20543,20660,23380,23451,23534,24157,24214,24272,24896", "endLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,35,37,38,39,40,42,44,45,46,47,48,50,52,54,56,58,60,61,66,68,70,71,72,74,76,77,78,79,80,85,139,142,185,188,203,214,216,218,221,225,228,229,230,233,234,235,236,237,238,241,242,244,246,248,250,254,256,257,258,259,261,265,267,269,270,271,272,273,274,276,311,312,322,323,324,336,348", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1748,1851,1954,2059,2166,2275,2384,2493,2602,2711,2818,2921,3040,3195,3350,3455,3576,3677,3824,3965,4068,4187,4294,4397,4552,4723,4872,5037,5194,5345,5464,5815,5964,6113,6225,6372,6525,6672,6747,6836,6923,7024,7551,11298,11483,14253,14450,15585,16361,16484,16597,16780,17035,17236,17325,17436,17669,17770,17865,17988,18117,18234,18411,18510,18645,18788,18923,19042,19243,19362,19455,19566,19622,19729,19924,20035,20168,20263,20354,20445,20538,20655,20794,23446,23529,24152,24209,24267,24891,25527"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\42c9cce8c7b5fed18c278c11b515454e\\transformed\\core-1.9.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,18,19,20,349,350,357,361,566,569", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1467,1531,1598,25532,25648,26105,26399,38562,38734", "endLines": "2,18,19,20,349,350,357,361,568,573", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1526,1593,1657,25643,25769,26226,26522,38729,39081"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\473d6f74a7df0a9331ff7f3743537b94\\transformed\\material-1.8.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,25,28,31,34,37,40,43,46,49,52,55,56,59,64,75,81,91,101,111,121,131,141,151,161,171,181,191,201,211,221,231,237,243,249,255,259,263,264,265,266,270,273,276,279,282,283,286,289,293,297", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,1960,2173,2432,2703,2921,3153,3389,3639,3852,4061,4292,4493,4609,4779,5100,6129,6586,7137,7692,8248,8809,9361,9912,10464,11017,11566,12119,12675,13230,13776,14330,14885,15177,15471,15771,16071,16400,16741,16879,17023,17179,17572,17790,18012,18238,18454,18564,18734,18924,19165,19424", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,51,54,55,58,63,74,80,90,100,110,120,130,140,150,160,170,180,190,200,210,220,230,236,242,248,254,258,262,263,264,265,269,272,275,278,281,282,285,288,292,296,299", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,1955,2168,2427,2698,2916,3148,3384,3634,3847,4056,4287,4488,4604,4774,5095,6124,6581,7132,7687,8243,8804,9356,9907,10459,11012,11561,12114,12670,13225,13771,14325,14880,15172,15466,15766,16066,16395,16736,16874,17018,17174,17567,17785,18007,18233,18449,18559,18729,18919,19160,19419,19596"}, "to": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,81,82,83,84,86,87,88,91,94,189,192,195,198,204,207,210,277,280,281,284,294,305,365,375,385,395,405,415,425,435,445,455,465,475,485,495,505,515,521,527,533,539,543,547,548,549,550,554,557,560,563,574,575,578,581,585,589", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "232,330,426,522,620,688,767,855,943,1031,1119,1206,1293,1380,7029,7122,7229,7334,7556,7681,7802,8015,8274,14455,14673,14905,15141,15590,15803,16012,20799,21000,21116,21286,21894,22923,26694,27245,27800,28356,28917,29469,30020,30572,31125,31674,32227,32783,33338,33884,34438,34993,35285,35579,35879,36179,36508,36849,36987,37131,37287,37680,37898,38120,38346,39086,39196,39366,39556,39797,40056", "endLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,81,82,83,84,86,87,90,93,96,191,194,197,200,206,209,212,279,280,283,288,304,310,374,384,394,404,414,424,434,444,454,464,474,484,494,504,514,520,526,532,538,542,546,547,548,549,553,556,559,562,565,574,577,580,584,588,591", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "325,421,517,615,683,762,850,938,1026,1114,1201,1288,1375,1462,7117,7224,7329,7448,7676,7797,8010,8269,8540,14668,14900,15136,15386,15798,16007,16238,20995,21111,21281,21602,22918,23375,27240,27795,28351,28912,29464,30015,30567,31120,31669,32222,32778,33333,33879,34433,34988,35280,35574,35874,36174,36503,36844,36982,37126,37282,37675,37893,38115,38341,38557,39191,39361,39551,39792,40051,40228"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\660657241239aba9a58fa535da30e4bc\\transformed\\exoplayer-ui-2.18.5\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endLines": "2,7", "endColumns": "58,10", "endOffsets": "109,396"}, "to": {"startLines": "3,289", "startColumns": "4,4", "startOffsets": "173,21607", "endLines": "3,293", "endColumns": "58,10", "endOffsets": "227,21889"}}, {"source": "C:\\gradle-home\\caches\\8.14.1\\transforms\\be2d4faa434f5fd45b18a8952ef333e2\\transformed\\media-1.6.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "351,354,358,362", "startColumns": "4,4,4,4", "startOffsets": "25774,25942,26231,26527", "endLines": "353,356,360,364", "endColumns": "12,12,12,12", "endOffsets": "25937,26100,26394,26689"}}]}]}